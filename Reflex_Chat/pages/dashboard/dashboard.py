import reflex as rx
from typing import List, Dict, Any, Tuple, Optional, TypedDict, Union, Sequence
from datetime import datetime
import httpx
import os
from Reflex_Chat.state import AuthState
from Reflex_Chat.components.navbar import navbar

class EvaluationType(TypedDict):
    score: float
    count: int

class CommentType(TypedDict):
    text: str
    evaluator: str
    role: str
    type: str

class CompetencyType(TypedDict):
    name: str
    description: str
    category: str
    factor: str  # Add factor field
    by_type: Dict[str, EvaluationType]
    by_role: Dict[str, EvaluationType]
    comments: List[CommentType]
    weighted_score: float

class DashboardState(AuthState):
    """State for the dashboard page."""

    # State variables
    user_data: Dict[str, Any] = {}
    active_projects: List[Dict[str, Any]] = []
    is_loading: bool = False

    # Score-related state variables
    competency_scores: Dict[str, CompetencyType] = {}  # Type hint for competency scores
    factor_scores: Dict[str, Dict[str, Any]] = {}  # Add factor scores
    category_scores: Dict[str, float] = {}
    final_score: float = 0
    selected_competency_id: str = ""  # Track the currently selected competency
    selected_category: str = ""  # Track the currently selected category

    # Category expansion state
    expanded_categories: List[str] = []  # Track which categories are expanded

    # Filter state variables
    project_options: List[Dict[str, str]] = [{"id": "0", "name": "All Projects"}]
    selected_project: str = "All Projects"  # Initialize with "All Projects"
    selected_project_id: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    score_data: Dict[str, Any] = {}

    # Evaluator role filter
    selected_evaluator_role: str = "All Roles"
    selected_evaluator_role_id: Optional[str] = None

    # New state variables for competency questions
    competency_questions_by_behavior: Optional[Dict[str, List[Dict[str, Any]]]] = None
    is_loading_questions: bool = False

    # Action plans state
    action_plans_by_factor: Dict[str, List[Dict[str, Any]]] = {}
    is_loading_action_plans: bool = False

    # Spider graph navigation state
    current_spider_factor_index: int = 0  # For navigating through factors in categories with multiple factors

    def __init__(self, parent_state=None, _reflex_internal_init=False, **kwargs):
        super().__init__(parent_state=parent_state, _reflex_internal_init=_reflex_internal_init, **kwargs)
        self.competency_scores = {}
        self.factor_scores = {}  # Initialize factor scores
        self.category_scores = {}
        self.final_score = 0
        self.selected_project_id = None
        self.start_date = None
        self.end_date = None
        self.selected_competency_id = ""
        self.selected_category = ""
        self.expanded_categories = []
        self.selected_evaluator_role_id = None
        self.competency_questions_by_behavior = None
        self.is_loading_questions = False
        self.action_plans_by_factor = {}
        self.is_loading_action_plans = False
        self.current_spider_factor_index = 0

    @rx.var
    def get_competency_items(self) -> List[Tuple[str, CompetencyType]]:
        """Convert competency_scores dictionary to a list of items."""
        if not self.competency_scores:
            return []
        return [(k, v) for k, v in self.competency_scores.items()]

    @rx.var
    def project_names(self) -> List[str]:
        """Get list of project names for the select component."""
        return [option["name"] for option in self.project_options]

    @rx.var
    def evaluator_role_names(self) -> List[str]:
        """Get list of evaluator role names for the select component."""
        # Extract unique evaluator roles from competency scores
        roles = set(["All Roles"])

        if self.competency_scores:
            for comp_data in self.competency_scores.values():
                if "by_role" in comp_data:
                    roles.update(comp_data["by_role"].keys())

        return sorted(list(roles))

    @rx.var
    def competency_names(self) -> List[Dict[str, str]]:
        """Get list of competency names for the dropdown."""
        if not self.competency_scores:
            return []
        return [{
            "id": comp_id,
            "name": comp_data["name"]
        } for comp_id, comp_data in self.competency_scores.items()]

    @rx.var
    def competency_name_list(self) -> List[str]:
        """Get list of competency names as strings for the select component."""
        if not self.competency_scores:
            return []
        return [comp_data["name"] for _, comp_data in self.competency_scores.items()]

    @rx.var
    def get_competencies_by_category(self) -> Dict[str, Dict[str, List[Tuple[str, CompetencyType]]]]:
        """Organize competencies by category and then by factor."""
        if not self.competency_scores:
            return {}

        # Group competencies by category and then by factor
        by_category = {}
        for comp_id, comp_data in self.competency_scores.items():
            category = comp_data["category"]
            factor = comp_data["factor"]
            # Format category name for display
            category_display = category.replace("_", " ").title()

            if category_display not in by_category:
                by_category[category_display] = {}

            if factor not in by_category[category_display]:
                by_category[category_display][factor] = []

            by_category[category_display][factor].append((comp_id, comp_data))

        # Define the desired order of categories
        category_order = [
            "Competencias Comportamentales",
            "Aprendizaje",
            "Competencias Tecnicas",
            "Feedback Cliente",
            "Feedback Manager"
        ]

        # Create a new ordered dictionary
        ordered_categories = {}
        for category in category_order:
            if category in by_category:
                ordered_categories[category] = by_category[category]

        return ordered_categories

    @rx.var
    def get_selected_competency(self) -> Optional[Tuple[str, CompetencyType]]:
        """Get the currently selected competency."""
        if not self.selected_competency_id or not self.competency_scores:
            # If no competency is selected or no competencies exist, return None
            return None

        # Try to get the selected competency
        if self.selected_competency_id in self.competency_scores:
            return (self.selected_competency_id, self.competency_scores[self.selected_competency_id])

        # If the selected competency doesn't exist, return None
        return None

    @rx.event(background=True)
    async def set_selected_competency(self, competency_name: str):
        """Set the currently selected competency based on the name."""
        # Find the competency ID that matches the selected name
        for comp in self.competency_names:
            if comp["name"] == competency_name:
                async with self:
                    self.selected_competency_id = comp["id"]
                    self.selected_category = ""  # Clear category selection when selecting competency

        # Fetch questions for this competency after setting the ID
        return DashboardState.fetch_competency_questions

    @rx.event
    def set_selected_category(self, category_name: str):
        """Set the currently selected category and clear competency selection."""
        self.selected_category = category_name
        self.selected_competency_id = ""  # Clear competency selection when selecting category
        self.current_spider_factor_index = 0  # Reset spider graph navigation

    @rx.event(background=True)
    async def fetch_competency_questions(self):
        """Fetch competency questions grouped by behavior type with response counts."""
        if not self._token or not self._token.get("oid") or not self.selected_competency_id:
            print("No token, oid, or selected competency found, skipping fetch_competency_questions")
            return

        async with self:
            self.is_loading_questions = True
            self.competency_questions_by_behavior = None

        user_id = self.db_user_id
        competency_id = self.selected_competency_id
        print(f"Fetching competency questions for user_id: {user_id}, competency_id: {competency_id}")

        api_url = f"{os.getenv('FASTAPI_URL', 'http://fastapi:8001')}/api/users/{user_id}/competency_questions/{competency_id}"
        print(f"Questions API URL: {api_url}")

        # Use the same filter parameters as fetch_score_data
        params = {}
        if self.selected_project_id:
            params['project_id'] = self.selected_project_id
            print(f"Adding project_id filter: {self.selected_project_id}")
        if self.start_date:
            params['start_date'] = self.start_date.isoformat()
            print(f"Adding start_date filter: {self.start_date.isoformat()}")
        if self.end_date:
            params['end_date'] = self.end_date.isoformat()
            print(f"Adding end_date filter: {self.end_date.isoformat()}")
        if self.selected_evaluator_role_id:
            params['evaluator_role'] = self.selected_evaluator_role_id
            print(f"Adding evaluator_role filter: {self.selected_evaluator_role_id}")

        print(f"Request params: {params}")

        try:
            async with httpx.AsyncClient() as client:
                print("Sending questions API request...")
                response = await client.get(api_url, params=params)

                #q Log the status code for debugging
                print(f"API response status code: {response.status_code}")

                # Check for specific status codes
                if response.status_code == 404:
                    print(f"Competency questions not found for competency_id={competency_id}")
                    async with self:
                        self.competency_questions_by_behavior = {}
                    return

                # Raise for other error status codes
                response.raise_for_status()

                # Parse the response
                data = response.json()
                print(f"Questions API response received with keys: {data.keys()}")

                # Add more detailed logging for debugging
                questions_by_behavior = data.get("questions_by_behavior", {})
                print(f"Questions by behavior structure: {questions_by_behavior.keys()}")

                # Ensure response_counts are properly initialized
                for behavior_type, questions in questions_by_behavior.items():
                    for question in questions:
                        # Initialize response_counts if not present
                        if 'response_counts' not in question:
                            question['response_counts'] = {'si': 0, 'no': 0, 'a_veces': 0}
                            continue

                        # Ensure response_counts has the expected keys with integer values
                        response_counts = question['response_counts']
                        if isinstance(response_counts, dict):
                            # Make sure all keys are present with integer values
                            if 'si' not in response_counts:
                                response_counts['si'] = 0
                            if 'no' not in response_counts:
                                response_counts['no'] = 0
                            if 'a_veces' not in response_counts:
                                response_counts['a_veces'] = 0

                            # Convert any non-integer values to integers
                            for key in ['si', 'no', 'a_veces']:
                                try:
                                    response_counts[key] = int(response_counts[key])
                                except (ValueError, TypeError):
                                    response_counts[key] = 0
                        else:
                            # If response_counts is not a dict, initialize it
                            question['response_counts'] = {'si': 0, 'no': 0, 'a_veces': 0}

                # Log a sample question if available
                for behavior_type, questions in questions_by_behavior.items():
                    if questions and len(questions) > 0:
                        sample_question = questions[0]
                        print(f"Sample question for {behavior_type}: {sample_question}")
                        if 'response_counts' in sample_question:
                            print(f"Sample response_counts: {sample_question['response_counts']}")
                        break

                async with self:
                    self.competency_questions_by_behavior = questions_by_behavior
                print(f"Received questions for {len(self.competency_questions_by_behavior)} behavior types")

        except httpx.HTTPStatusError as e:
            print(f"API Status Error in fetch_competency_questions: {e.response.status_code} - {e.response.text}")
            async with self:
                self.competency_questions_by_behavior = {}
        except httpx.RequestError as e:
            print(f"API Request Error in fetch_competency_questions: {str(e)}")
            async with self:
                self.competency_questions_by_behavior = {}
        except httpx.HTTPError as e:
            print(f"API Error in fetch_competency_questions: {str(e)}")
            async with self:
                self.competency_questions_by_behavior = {}
        except Exception as e:
            print(f"Unexpected error in fetch_competency_questions: {str(e)}")
            async with self:
                self.competency_questions_by_behavior = {}
        finally:
            async with self:
                self.is_loading_questions = False

    @rx.event(background=True)
    async def fetch_action_plans(self):
        """Fetch action plans for the current user."""
        if not self._token or not self._token.get("oid"):
            print("No token or oid found, skipping fetch_action_plans")
            return

        async with self:
            self.is_loading_action_plans = True
            self.action_plans_by_factor = {}

        user_id = self.db_user_id
        print(f"Fetching action plans for user_id: {user_id}")

        api_url = f"{os.getenv('FASTAPI_URL', 'http://fastapi:8001')}/api/action-plans/user/{user_id}"
        print(f"Action Plans API URL: {api_url}")

        # Use the same filter parameters as fetch_score_data
        params = {}
        if self.selected_project_id:
            params['project_id'] = self.selected_project_id
            print(f"Adding project_id filter: {self.selected_project_id}")
        if self.start_date:
            params['start_date'] = self.start_date.isoformat()
            print(f"Adding start_date filter: {self.start_date.isoformat()}")
        if self.end_date:
            params['end_date'] = self.end_date.isoformat()
            print(f"Adding end_date filter: {self.end_date.isoformat()}")

        print(f"Request params: {params}")

        try:
            async with httpx.AsyncClient() as client:
                print("Sending action plans API request...")
                response = await client.get(api_url, params=params)

                # Log the status code for debugging
                print(f"API response status code: {response.status_code}")

                # Check for specific status codes
                if response.status_code == 404:
                    print(f"No action plans found for user_id={user_id}")
                    async with self:
                        self.action_plans_by_factor = {}
                    return

                # Raise for other error status codes
                response.raise_for_status()

                # Parse the response
                data = response.json()
                print(f"Action Plans API response received with keys: {data.keys()}")

                async with self:
                    self.action_plans_by_factor = data
                print(f"Received action plans for {len(self.action_plans_by_factor)} factors")

        except httpx.HTTPStatusError as e:
            print(f"API Status Error in fetch_action_plans: {e.response.status_code} - {e.response.text}")
            async with self:
                self.action_plans_by_factor = {}
        except httpx.RequestError as e:
            print(f"API Request Error in fetch_action_plans: {str(e)}")
            async with self:
                self.action_plans_by_factor = {}
        except httpx.HTTPError as e:
            print(f"API Error in fetch_action_plans: {str(e)}")
            async with self:
                self.action_plans_by_factor = {}
        except Exception as e:
            print(f"Unexpected error in fetch_action_plans: {str(e)}")
            async with self:
                self.action_plans_by_factor = {}
        finally:
            async with self:
                self.is_loading_action_plans = False

    @rx.event(background=True)
    async def fetch_user_data(self):
        """Fetch user dashboard data from API."""
        if not self._token or not self._token.get("oid"):
            return

        user_id = self.db_user_id
        api_url = f"{os.getenv('FASTAPI_URL', 'http://fastapi:8001')}/api/users/{user_id}/user_role_project_summary"

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(api_url)
                response.raise_for_status()
                data = response.json()

                async with self:
                    self.user_data = data["user"]
                    project_dicts = [{"id": str(p["id"]), "name": p["name"]} for p in data["active_projects"]]
                    self.project_options = [{"id": "0", "name": "All Projects"}] + project_dicts

        except httpx.HTTPError as e:
            print(f"API Error: {str(e)}")

    @rx.event(background=True)
    async def fetch_score_data(self):
        """Fetch score calculation data from API."""
        if not self._token or not self._token.get("oid"):
            print("No token or oid found, skipping fetch_score_data")
            return

        user_id = self.db_user_id
        print(f"Fetching score data for user_id: {user_id}")

        api_url = f"{os.getenv('FASTAPI_URL', 'http://fastapi:8001')}/api/users/{user_id}/evaluation_score_calculator"
        print(f"Score API URL: {api_url}")

        # Use the same filter parameters as fetch_score_data
        params = {}
        if self.selected_project_id:
            params['project_id'] = self.selected_project_id
            print(f"Adding project_id filter: {self.selected_project_id}")
        if self.start_date:
            params['start_date'] = self.start_date.isoformat()
            print(f"Adding start_date filter: {self.start_date.isoformat()}")
        if self.end_date:
            params['end_date'] = self.end_date.isoformat()
            print(f"Adding end_date filter: {self.end_date.isoformat()}")
        if self.selected_evaluator_role_id:
            params['evaluator_role'] = self.selected_evaluator_role_id
            print(f"Adding evaluator_role filter: {self.selected_evaluator_role_id}")

        print(f"Request params: {params}")

        try:
            async with httpx.AsyncClient() as client:
                print("Sending score API request...")
                response = await client.get(api_url, params=params)
                response.raise_for_status()
                data = response.json()
                print(f"Score API response received with keys: {data.keys()}")

                async with self:
                    self.score_data = data
                    if 'overall_score' in data and 'final_score' in data['overall_score']:
                        self.final_score = data['overall_score']['final_score']
                        print(f"Final score set: {self.final_score}")
                    # Use root-level category_scores instead of overall_score.category_scores
                    if 'category_scores' in data:
                        self.category_scores = {}
                        # Extract the weighted_score from each category
                        for category_key, category_data in data['category_scores'].items():
                            if 'weighted_score' in category_data:
                                self.category_scores[category_key] = category_data['weighted_score']
                                print(f"Category {category_key} score set to {category_data['weighted_score']} from root-level")

                                # Check if there's a matching factor score for comparison
                                for factor_key, factor_data in data.get('factor_scores', {}).items():
                                    if category_key in factor_key and 'weighted_score' in factor_data:
                                        print(f"  Matching factor {factor_key} score: {factor_data['weighted_score']}")

                        print(f"Category scores set with {len(self.category_scores)} categories from root-level category_scores")
                    elif 'overall_score' in data and 'category_scores' in data['overall_score']:
                        # Fallback to overall_score.category_scores if root-level is not available
                        self.category_scores = data['overall_score']['category_scores']
                        print(f"Category scores set with {len(self.category_scores)} categories from overall_score.category_scores")
                    if 'competency_scores' in data:
                        self.competency_scores = data['competency_scores']
                        print(f"Competency scores set with {len(self.competency_scores)} competencies")
                    if 'factor_scores' in data:
                        self.factor_scores = data['factor_scores']
                        print(f"Factor scores set with {len(self.factor_scores)} factors")
                        print("Factor scores data:", self.factor_scores)  # Debug print
                        print("Factor scores keys:", list(self.factor_scores.keys()))  # Debug print
                        print("Sample factor score:", next(iter(self.factor_scores.values())) if self.factor_scores else None)  # Debug print

                        # Set the first competency as selected if there are competencies and none is selected
                        if self.competency_scores and not self.selected_competency_id:
                            self.selected_competency_id = next(iter(self.competency_scores.keys()), "")

                        # Initialize expanded categories
                        # Group competencies by category
                        categories = set()
                        for comp_data in self.competency_scores.values():
                            category = comp_data["category"]
                            category_display = category.replace("_", " ").title()
                            categories.add(category_display)

                        # Initialize all categories as collapsed by default (empty list means all are expanded)
                        self.expanded_categories = []

        except httpx.HTTPError as e:
            print(f"API Error in fetch_score_data: {str(e)}")
        except Exception as e:
            print(f"Unexpected error in fetch_score_data: {str(e)}")

    @rx.event(background=True)
    async def on_load(self):
        """Load initial data when the page loads."""
        async with self:
            self.is_loading = True
        yield self.fetch_user_data()
        yield self.fetch_score_data()
        async with self:
            self.is_loading = False

    @rx.event(background=True)
    async def on_authenticated_load(self):
        """Enforce auth and then load dashboard data."""
        self.require_auth()
        async with self:
            self.is_loading = True
        yield DashboardState.fetch_user_data
        yield DashboardState.fetch_score_data
        yield DashboardState.fetch_action_plans
        async with self:
            self.is_loading = False

    @rx.event(background=True)
    async def set_project_filter(self, project_name: str):
        """Update project filter and refresh data."""
        async with self:
            self.selected_project = project_name

            # Find the project ID that matches the selected name
            if project_name == "All Projects":
                self.selected_project_id = None
            else:
                selected_project = next(
                    (p for p in self.project_options if p["name"] == project_name),
                    None
                )
                if selected_project:
                    self.selected_project_id = int(selected_project["id"])
                else:
                    self.selected_project_id = None

        # Use DashboardState class reference instead of self
        yield DashboardState.fetch_score_data
        yield DashboardState.fetch_action_plans

        # If a competency is selected, also refresh the questions
        if self.selected_competency_id:
            yield DashboardState.fetch_competency_questions

    @rx.event(background=True)
    async def set_start_date(self, date_str: str):
        """Update start date filter and refresh data."""
        async with self:
            if date_str:
                try:
                    # Convert the string date from the input to a datetime object
                    self.start_date = datetime.fromisoformat(date_str)
                    print(f"Start date set to: {self.start_date}")
                except ValueError as e:
                    print(f"Error parsing start date: {e}")
                    self.start_date = None
            else:
                self.start_date = None

        # Use DashboardState class reference instead of self
        yield DashboardState.fetch_score_data
        yield DashboardState.fetch_action_plans

        # If a competency is selected, also refresh the questions
        if self.selected_competency_id:
            yield DashboardState.fetch_competency_questions

    @rx.event(background=True)
    async def set_end_date(self, date_str: str):
        """Update end date filter and refresh data."""
        async with self:
            if date_str:
                try:
                    # Convert the string date from the input to a datetime object
                    self.end_date = datetime.fromisoformat(date_str)
                    print(f"End date set to: {self.end_date}")
                except ValueError as e:
                    print(f"Error parsing end date: {e}")
                    self.end_date = None
            else:
                self.end_date = None

        # Use DashboardState class reference instead of self
        yield DashboardState.fetch_score_data
        yield DashboardState.fetch_action_plans

        # If a competency is selected, also refresh the questions
        if self.selected_competency_id:
            yield DashboardState.fetch_competency_questions

    @rx.event(background=True)
    async def reset_filters(self):
        """Reset all filters to default values."""
        async with self:
            self.selected_project = "All Projects"
            self.selected_project_id = None
            self.start_date = None
            self.end_date = None
            self.selected_evaluator_role = "All Roles"
            self.selected_evaluator_role_id = None

        # Use DashboardState class reference instead of self
        yield DashboardState.fetch_score_data
        yield DashboardState.fetch_action_plans

        # If a competency is selected, also refresh the questions
        if self.selected_competency_id:
            yield DashboardState.fetch_competency_questions

    @rx.event(background=True)
    async def set_evaluator_role_filter(self, role_name: str):
        """Update evaluator role filter and refresh data."""
        async with self:
            self.selected_evaluator_role = role_name

            # Find the role ID that matches the selected name
            if role_name == "All Roles":
                self.selected_evaluator_role_id = None
            else:
                self.selected_evaluator_role_id = role_name

        # Use DashboardState class reference instead of self
        yield DashboardState.fetch_score_data
        yield DashboardState.fetch_action_plans

        # If a competency is selected, also refresh the questions
        if self.selected_competency_id:
            yield DashboardState.fetch_competency_questions

    @rx.event
    async def toggle_category_expansion(self, category: str):
        """Toggle the expanded state of a category."""
        # If category is in the list, remove it (collapse)
        async with self:
            if category in self.expanded_categories:
                self.expanded_categories.remove(category)
            else:
                # Otherwise add it (expand)
                self.expanded_categories.append(category)

    @rx.var
    def get_expanded_categories(self) -> List[str]:
        """Get the list of expanded categories."""
        return self.expanded_categories

    @rx.var
    def has_action_plans(self) -> bool:
        """Check if there are any action plans."""
        return len(self.action_plans_by_factor) > 0

    @rx.var
    def get_action_plans_for_current_factor(self) -> List[Dict[str, Any]]:
        """Get action plans for the current competency's factor."""
        if not self.get_selected_competency:
            return []

        current_factor = self.get_selected_competency[1]["factor"]

        # Look for action plans that match this factor
        for factor_key, action_plans in self.action_plans_by_factor.items():
            if current_factor in factor_key:
                return action_plans

        return []

    @rx.var
    def get_action_plans_for_current_category(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get action plans for the current category, organized by factor."""
        if not self.selected_category:
            return {}

        # Convert display category name back to internal format
        # Frontend: "Competencias Tecnicas" -> Backend: "competencias_tecnicas"
        # Frontend: "Competencias Comportamentales" -> Backend: "competencias_comportamentales"
        category_mapping = {
            "Competencias Tecnicas": "competencias_tecnicas",
            "Competencias Comportamentales": "competencias_comportamentales",
            "Feedback Cliente": "feedback_cliente",
            "Feedback Manager": "feedback_manager",
            "Aprendizaje": "aprendizaje"
        }

        category_internal = category_mapping.get(self.selected_category)
        if not category_internal:
            return {}

        # Find all action plans that belong to this category
        category_action_plans = {}
        for factor_key, action_plans in self.action_plans_by_factor.items():
            # factor_key format is "category_factor" (e.g., "competencias_tecnicas_Bloque 1: Enmarcar el Problema")
            if factor_key.startswith(category_internal + "_"):
                # Extract just the factor name
                factor_name = factor_key.replace(f"{category_internal}_", "")
                category_action_plans[factor_name] = action_plans
        return category_action_plans

    def next_spider_factor(self):
        """Navigate to the next factor in the spider graph."""
        factors = self.get_factors_for_current_category
        if factors and self.current_spider_factor_index < len(factors) - 1:
            self.current_spider_factor_index += 1

    def prev_spider_factor(self):
        """Navigate to the previous factor in the spider graph."""
        if self.current_spider_factor_index > 0:
            self.current_spider_factor_index -= 1

    @rx.var
    def get_factors_for_current_category(self) -> List[str]:
        """Get the list of factors for the current category."""
        if not self.selected_category:
            return []

        # Convert display category name back to internal format
        category_mapping = {
            "Competencias Tecnicas": "competencias_tecnicas",
            "Competencias Comportamentales": "competencias_comportamentales",
            "Feedback Cliente": "feedback_cliente",
            "Feedback Manager": "feedback_manager",
            "Aprendizaje": "aprendizaje"
        }

        category_internal = category_mapping.get(self.selected_category)
        if not category_internal:
            return []

        # Get unique factors for this category from competency scores
        factors = set()
        for comp_id, comp_data in self.competency_scores.items():
            if comp_data.get("category") == category_internal:
                factor = comp_data.get("factor")
                if factor:
                    factors.add(factor)

        return sorted(list(factors))

    @rx.var
    def get_current_spider_factor(self) -> str:
        """Get the current factor for the spider graph."""
        factors = self.get_factors_for_current_category
        if factors and 0 <= self.current_spider_factor_index < len(factors):
            return factors[self.current_spider_factor_index]
        return ""

    @rx.var
    def get_spider_chart_data(self) -> List[Dict[str, Any]]:
        """Get data for the current spider chart."""
        current_factor = self.get_current_spider_factor
        if not current_factor or not self.selected_category:
            return []

        # Convert display category name back to internal format
        category_mapping = {
            "Competencias Tecnicas": "competencias_tecnicas",
            "Competencias Comportamentales": "competencias_comportamentales",
            "Feedback Cliente": "feedback_cliente",
            "Feedback Manager": "feedback_manager",
            "Aprendizaje": "aprendizaje"
        }

        category_internal = category_mapping.get(self.selected_category)
        if not category_internal:
            return []

        # Get competencies for this factor
        chart_data = []
        for comp_id, comp_data in self.competency_scores.items():
            if (comp_data.get("category") == category_internal and
                comp_data.get("factor") == current_factor):
                chart_data.append({
                    "competency": comp_data.get("name", ""),
                    "score": comp_data.get("weighted_score", 0)
                })

        return chart_data

    @rx.var
    def has_multiple_factors(self) -> bool:
        """Check if the current category has multiple factors."""
        factors = self.get_factors_for_current_category
        return len(factors) > 1

    @rx.var
    def factor_count(self) -> int:
        """Get the number of factors for the current category."""
        factors = self.get_factors_for_current_category
        return len(factors)

    @rx.var
    def has_spider_chart_data(self) -> bool:
        """Check if there is data for the spider chart."""
        chart_data = self.get_spider_chart_data
        return len(chart_data) > 0

    @rx.var
    def has_current_spider_factor(self) -> bool:
        """Check if there is a current spider factor."""
        current_factor = self.get_current_spider_factor
        return current_factor != ""

def spider_chart_component() -> rx.Component:
    """Component for displaying spider/radar charts."""
    return rx.cond(
        DashboardState.selected_category != "",
        rx.vstack(
            # Chart header with navigation
            rx.hstack(
                rx.heading(
                    "Resumen Visual",
                    size="3",
                    color=rx.color("gray", 11),
                ),
                rx.spacer(),
                # Navigation controls (only show if multiple factors)
                rx.cond(
                    DashboardState.has_multiple_factors,
                    rx.hstack(
                        rx.button(
                            rx.icon("chevron-left"),
                            on_click=DashboardState.prev_spider_factor,
                            size="1",
                            variant="soft",
                            disabled=DashboardState.current_spider_factor_index == 0,
                        ),
                        rx.text(
                            DashboardState.current_spider_factor_index + 1,
                            " / ",
                            DashboardState.factor_count,
                            size="2",
                            color=rx.color("gray", 10),
                        ),
                        rx.button(
                            rx.icon("chevron-right"),
                            on_click=DashboardState.next_spider_factor,
                            size="1",
                            variant="soft",
                            disabled=DashboardState.current_spider_factor_index >= DashboardState.factor_count - 1,
                        ),
                        spacing="2",
                        align_items="center",
                    ),
                ),
                width="100%",
                justify="between",
                align_items="center",
                margin_bottom="3",
            ),
            # Factor name
            rx.cond(
                DashboardState.has_current_spider_factor,
                rx.text(
                    DashboardState.get_current_spider_factor,
                    size="2",
                    color=rx.color("gray", 10),
                    margin_bottom="3",
                ),
            ),
            # Spider chart
            rx.cond(
                DashboardState.has_spider_chart_data,
                rx.recharts.radar_chart(
                    rx.recharts.radar(
                        data_key="score",
                        stroke="#8b5cf6",
                        fill="#8b5cf6",
                        fill_opacity=0.3,
                    ),
                    rx.recharts.polar_angle_axis(data_key="competency"),
                    rx.recharts.polar_radius_axis(
                        angle=90,
                        domain=[0, 100],
                    ),
                    rx.recharts.polar_grid(),
                    data=DashboardState.get_spider_chart_data,
                    width="100%",
                    height=300,
                ),
                # No data message
                rx.center(
                    rx.text(
                        "No hay datos disponibles para mostrar",
                        color=rx.color("gray", 10),
                        size="2",
                    ),
                    height="200px",
                ),
            ),
            width="100%",
            spacing="3",
            padding="4",
            border_radius="md",
            border="1px solid #e5e7eb",
            background="#f9fafb",
        ),
    )

#page

def sidebar_filters() -> rx.Component:
    """Sidebar component with user info and filters."""
    return rx.box(
        rx.vstack(
            # User Info Section
            rx.cond(
                DashboardState.user_data.get("name") != None,
                rx.vstack(
                    rx.icon("scan-face"),
                    rx.heading(f"Bienvenido, {DashboardState.user_data.get('name', '')}", size="4", color="blue.500"),
                    rx.badge(f"{DashboardState.user_data.get('role', '')}", font_weight="medium"),
                    align_items="start",
                    width="100%",
                ),
            ),

            rx.divider(),

            rx.vstack(
                rx.heading("Resultado Total:", size="3", width="100%", margin_bottom="3"),
                rx.badge(
                    rx.text(
                        f"{DashboardState.final_score:.2f}%",
                        size="2",
                        color="white",
                    ),
                    variant="solid",
                    size="2",
                    color_scheme="teal",
                ),
                text_align="center",
                align_items="left",
            ),

            rx.divider(),

            # Filters Section
            rx.vstack(
                rx.hstack(
                    rx.heading("Filtros", size="3", margin_bottom="4"),
                    # Reset button
                    rx.button(
                        "Reset",
                        on_click=DashboardState.reset_filters,
                        size="1",
                        variant="soft",
                        color_scheme="red",
                        width="30%",
                    ),
                    width="100%",
                    justify="between",
                    align="center",
                ),
                # Project filter
                rx.vstack(
                    rx.text("Proyecto:", font_weight="medium"),
                    rx.select(
                        DashboardState.project_names,
                        placeholder="Selecciona un proyecto",
                        on_change=DashboardState.set_project_filter,
                        value=DashboardState.selected_project,
                        width="100%"
                    ),
                    align_items="start",
                    width="100%",
                    spacing="1",
                    margin_bottom="4",
                ),
                # Evaluator role filter
                rx.vstack(
                    rx.text("Rol del Evaluador:", font_weight="medium"),
                    rx.select(
                        DashboardState.evaluator_role_names,
                        placeholder="Selecciona un rol",
                        on_change=DashboardState.set_evaluator_role_filter,
                        value=DashboardState.selected_evaluator_role,
                        width="100%"
                    ),
                    align_items="start",
                    width="100%",
                    spacing="1",
                    margin_bottom="4",
                ),
                rx.box(
                    rx.vstack(   # Date filters
                        rx.vstack(
                            rx.text("Fecha Inicio:", font_weight="medium"),
                            rx.input(
                                type="date",
                                on_change=DashboardState.set_start_date,
                                width="100%"
                            ),
                            align_items="start",
                            width="100%",
                            spacing="1",
                            margin_bottom="4",
                        ),

                        rx.vstack(
                            rx.text("Fecha Fin:", font_weight="medium"),
                            rx.input(
                                type="date",
                                on_change=DashboardState.set_end_date,
                                width="100%"
                            ),
                            align_items="start",
                            width="100%",
                            spacing="1",
                            margin_bottom="4",
                        ),
                        width="100%",
                        spacing="4",
                    ),
                    width="100%",
                    spacing="2",
                    padding="4",
                ),
            ),
            height="100%",
            width="100%",
            spacing="4",
        ),
        width="12%",
        padding="1%",
        height="calc(100vh - 4rem)",
        background_color=rx.color("mauve", 2),
        overflow_y="auto",
        flex_shrink="0"
    )

@rx.page(route="/dashboard", title="Dashboard", on_load=DashboardState.on_authenticated_load)
def dashboard() -> rx.Component:
    """The dashboard page."""
    return rx.box(
        # Add script to prevent body scrolling
        rx.script("document.body.style.overflow = 'hidden'; document.body.style.height = '100vh'; document.body.style.margin = '0'; document.body.style.padding = '0';"),
        navbar(),
        #background_v3(),

        rx.hstack(
            # Sidebar with filters
            sidebar_filters(),

            # Main content area
            rx.box(
                rx.cond(
                    DashboardState.is_loading,
                    rx.center(rx.spinner(size="3")),

                    rx.vstack(

                    # Scores Section
                    rx.vstack(

                        # rx.box(
                        #     rx.divider(),
                        #     width="100%",
                        #     padding_x="1%",
                        #     padding_y="0%",
                        # ),

                        # Competency Scores Section
                        rx.box(
                            #rx.heading("Competencias por Categoría", size="4", margin_bottom="4"),
                            #rx.divider(margin_y="15px"),  # Add vertical margin to create space

                            # New Card with Left Menu and Competency Details
                            rx.box(
                                rx.flex(
                                    # Left sidebar menu with competencies by category
                                    rx.box(
                                        rx.box(
                                            rx.vstack(
                                                rx.foreach(
                                                    DashboardState.get_competencies_by_category.items(),
                                                    lambda category_item: rx.box(
                                                        rx.vstack(
                                                            # Category Header with Badge - Now Clickable
                                                            rx.button(
                                                                rx.hstack(
                                                                    rx.badge(
                                                                        rx.text(
                                                                            category_item[0],
                                                                            text_overflow="ellipsis",
                                                                            overflow="hidden",
                                                                            white_space="nowrap",
                                                                        ),
                                                                        color_scheme=rx.cond(
                                                                            category_item[0] == "Competencias Comportamentales",
                                                                            "crimson",
                                                                            rx.cond(
                                                                                category_item[0] == "Competencias Tecnicas",
                                                                                "violet",
                                                                                rx.cond(
                                                                                    category_item[0] == "Aprendizaje",
                                                                                    "indigo",
                                                                                    rx.cond(
                                                                                        category_item[0] == "Feedback Cliente",
                                                                                        "crimson",
                                                                                        rx.cond(
                                                                                            category_item[0] == "Feedback Manager",
                                                                                            "grass",
                                                                                            "orange"
                                                                                        )
                                                                                    )
                                                                                )
                                                                            )
                                                                        ),
                                                                        variant="solid",
                                                                        size="1",
                                                                        border_radius="md",
                                                                        max_width="70%",
                                                                    ),
                                                                    rx.badge(
                                                                        # Add debug logging
                                                                        rx.script(f"""
                                                                            console.log("Category display: {category_item[0]}");
                                                                            console.log("Category key: {category_item[0].lower().replace(' ', '_')}");
                                                                            console.log("Category score: " + {DashboardState.category_scores.get(category_item[0].lower().replace(' ', '_'), 0)});
                                                                        """),
                                                                        f"{DashboardState.category_scores.get(category_item[0].lower().replace(' ', '_'), 0):.1f}%",
                                                                        color_scheme=rx.cond(
                                                                            category_item[0] == "Competencias Comportamentales",
                                                                            "crimson",
                                                                            rx.cond(
                                                                                category_item[0] == "Competencias Tecnicas",
                                                                                "violet",
                                                                                rx.cond(
                                                                                    category_item[0] == "Aprendizaje",
                                                                                    "indigo",
                                                                                    rx.cond(
                                                                                        category_item[0] == "Feedback Cliente",
                                                                                        "crimson",
                                                                                        rx.cond(
                                                                                            category_item[0] == "Feedback Manager",
                                                                                            "grass",
                                                                                            "orange"
                                                                                        )
                                                                                    )
                                                                                )
                                                                            )
                                                                        ),
                                                                        size="2",
                                                                        text_align="right",
                                                                        font_weight="bold",
                                                                        max_width="30%",
                                                                    ),
                                                                    width="100%",
                                                                    justify="between",
                                                                    align_items="center",
                                                                    spacing="2",
                                                                ),
                                                                on_click=DashboardState.set_selected_category(category_item[0]),
                                                                variant="ghost",
                                                                size="2",
                                                                width="100%",
                                                                justify_content="center",
                                                                padding_y="2",
                                                                padding_x="3",
                                                                border_radius="md",
                                                                background=rx.cond(
                                                                    category_item[0] == DashboardState.selected_category,
                                                                    rx.color(
                                                                        rx.cond(
                                                                            category_item[0] == "Competencias Comportamentales",
                                                                            "crimson",
                                                                            rx.cond(
                                                                                category_item[0] == "Competencias Tecnicas",
                                                                                "violet",
                                                                                rx.cond(
                                                                                    category_item[0] == "Aprendizaje",
                                                                                    "indigo",
                                                                                    rx.cond(
                                                                                        category_item[0] == "Feedback Cliente",
                                                                                        "crimson",
                                                                                        rx.cond(
                                                                                            category_item[0] == "Feedback Manager",
                                                                                            "grass",
                                                                                            "orange"
                                                                                        )
                                                                                    )
                                                                                )
                                                                            )
                                                                        ), 3
                                                                    ),
                                                                    "transparent"
                                                                ),
                                                                border_left=rx.cond(
                                                                    category_item[0] == DashboardState.selected_category,
                                                                    f"6px solid {rx.color(rx.cond(category_item[0] == 'Competencias Comportamentales', 'crimson', rx.cond(category_item[0] == 'Competencias Tecnicas', 'violet', rx.cond(category_item[0] == 'Aprendizaje', 'indigo', rx.cond(category_item[0] == 'Feedback Cliente', 'crimson', rx.cond(category_item[0] == 'Feedback Manager', 'grass', 'orange'))))), 9)}",
                                                                    "6px solid transparent"
                                                                ),
                                                                _hover={
                                                                    "background": rx.color(
                                                                        rx.cond(
                                                                            category_item[0] == "Competencias Comportamentales",
                                                                            "crimson",
                                                                            rx.cond(
                                                                                category_item[0] == "Competencias Tecnicas",
                                                                                "violet",
                                                                                rx.cond(
                                                                                    category_item[0] == "Aprendizaje",
                                                                                    "indigo",
                                                                                    rx.cond(
                                                                                        category_item[0] == "Feedback Cliente",
                                                                                        "crimson",
                                                                                        rx.cond(
                                                                                            category_item[0] == "Feedback Manager",
                                                                                            "grass",
                                                                                            "orange"
                                                                                        )
                                                                                    )
                                                                                )
                                                                            )
                                                                        ), 2
                                                                    ),
                                                                },
                                                            ),
                                                            rx.divider(),
                                                            # Factors and Competencies
                                                            rx.vstack(
                                                                rx.foreach(
                                                                    category_item[1].items(),
                                                                    lambda factor_item: rx.vstack(
                                                                        # Factor Header
                                                                        rx.box(
                                                                            rx.button(
                                                                                rx.hstack(
                                                                                    rx.text(
                                                                                        factor_item[0],
                                                                                        font_weight="bold",
                                                                                        color=rx.color("gray", 11),
                                                                                        font_size="sm",
                                                                                        flex="1",
                                                                                    ),
                                                                                    rx.text(
                                                                                        f"{DashboardState.factor_scores.get(factor_item[0] + '_CompetencyCategory.' + category_item[0].upper().replace(' ', '_'), {}).get('weighted_score', 0):.1f}%",
                                                                                        size="1",
                                                                                        variant="soft",
                                                                                        color_scheme=rx.cond(
                                                                                            category_item[0] == "Competencias Comportamentales",
                                                                                            "crimson",
                                                                                            rx.cond(
                                                                                                category_item[0] == "Competencias Tecnicas",
                                                                                                "violet",
                                                                                                rx.cond(
                                                                                                    category_item[0] == "Aprendizaje",
                                                                                                    "indigo",
                                                                                                    rx.cond(
                                                                                                        category_item[0] == "Feedback Cliente",
                                                                                                        "crimson",
                                                                                                        rx.cond(
                                                                                                            category_item[0] == "Feedback Manager",
                                                                                                            "grass",
                                                                                                            "orange"
                                                                                                        )
                                                                                                    )
                                                                                                )
                                                                                            )
                                                                                        ),
                                                                                        flex_shrink="0",
                                                                                    ),
                                                                                    width="100%",
                                                                                    justify="between",
                                                                                    spacing="2",
                                                                                ),
                                                                                variant="ghost",
                                                                                size="2",
                                                                                width="100%",
                                                                                justify_content="center",
                                                                                padding_y="1",
                                                                                padding_x="3",
                                                                                border_radius="md",
                                                                                text_align="center",
                                                                                overflow="hidden",
                                                                                background=rx.color("gray", 1),
                                                                                _hover={
                                                                                    "background": rx.color("gray", 2),
                                                                                    "color": rx.color("gray", 12)
                                                                                },
                                                                            ),
                                                                            #padding_x="3",
                                                                            #padding_y="2",
                                                                            #margin_bottom="2",
                                                                        ),
                                                                        # Competencies in this factor
                                                                        rx.vstack(
                                                                            rx.foreach(
                                                                                factor_item[1],
                                                                                lambda comp_item: rx.box(
                                                                                    rx.button(
                                                                                        rx.hstack(
                                                                                            rx.text(
                                                                                                comp_item[1]["name"].to(str),
                                                                                                text_overflow="ellipsis",
                                                                                                overflow="hidden",
                                                                                                white_space="nowrap",
                                                                                                flex="1",
                                                                                            ),
                                                                                            rx.text(
                                                                                                f"{comp_item[1]['weighted_score'].to(float):.1f}%",
                                                                                                size="1",
                                                                                                variant="soft",
                                                                                                color_scheme=rx.cond(
                                                                                                    category_item[0] == "Competencias Comportamentales",
                                                                                                    "crimson",
                                                                                                    rx.cond(
                                                                                                        category_item[0] == "Competencias Tecnicas",
                                                                                                        "violet",
                                                                                                        rx.cond(
                                                                                                            category_item[0] == "Aprendizaje",
                                                                                                            "indigo",
                                                                                                            rx.cond(
                                                                                                                category_item[0] == "Feedback Cliente",
                                                                                                                "crimson",
                                                                                                                rx.cond(
                                                                                                                    category_item[0] == "Feedback Manager",
                                                                                                                    "grass",
                                                                                                                    "orange"
                                                                                                                )
                                                                                                            )
                                                                                                        )
                                                                                                    )
                                                                                                ),
                                                                                                flex_shrink="0",
                                                                                            ),
                                                                                            width="100%",
                                                                                            justify="between",
                                                                                            spacing="2",
                                                                                        ),
                                                                                        on_click=DashboardState.set_selected_competency(comp_item[1]["name"]),
                                                                                        variant="ghost",
                                                                                        size="1",
                                                                                        width="100%",
                                                                                        justify_content="flex-start",
                                                                                        #padding_y="1",
                                                                                        padding_x="3",
                                                                                        border_radius="md",
                                                                                        text_align="left",
                                                                                        overflow="hidden",
                                                                                        background=rx.cond(
                                                                                            comp_item[0] == DashboardState.selected_competency_id,
                                                                                            rx.color(
                                                                                                rx.cond(
                                                                                                    category_item[0] == "Competencias Comportamentales",
                                                                                                    "crimson",
                                                                                                    rx.cond(
                                                                                                        category_item[0] == "Competencias Tecnicas",
                                                                                                        "violet",
                                                                                                        rx.cond(
                                                                                                            category_item[0] == "Aprendizaje",
                                                                                                            "indigo",
                                                                                                            rx.cond(
                                                                                                                category_item[0] == "Feedback Cliente",
                                                                                                                "crimson",
                                                                                                                rx.cond(
                                                                                                                    category_item[0] == "Feedback Manager",
                                                                                                                    "grass",
                                                                                                                    "orange"
                                                                                                                )
                                                                                                            )
                                                                                                        )
                                                                                                    )
                                                                                                ), 3
                                                                                            ),
                                                                                            "transparent"
                                                                                        ),
                                                                                        border_left=rx.cond(
                                                                                            comp_item[0] == DashboardState.selected_competency_id,
                                                                                            f"6px solid {rx.color(rx.cond(category_item[0] == 'Competencias Comportamentales', 'crimson', rx.cond(category_item[0] == 'Competencias Tecnicas', 'violet', rx.cond(category_item[0] == 'Aprendizaje', 'indigo', rx.cond(category_item[0] == 'Feedback Cliente', 'crimson', rx.cond(category_item[0] == 'Feedback Manager', 'grass', 'orange'))))), 9)}",
                                                                                            "6px solid transparent"
                                                                                        ),
                                                                                        color=rx.cond(
                                                                                            comp_item[0] == DashboardState.selected_competency_id,
                                                                                            rx.color(
                                                                                                rx.cond(
                                                                                                    category_item[0] == "Competencias Comportamentales",
                                                                                                    "crimson",
                                                                                                    rx.cond(
                                                                                                        category_item[0] == "Competencias Tecnicas",
                                                                                                        "violet",
                                                                                                        rx.cond(
                                                                                                            category_item[0] == "Aprendizaje",
                                                                                                            "indigo",
                                                                                                            rx.cond(
                                                                                                                category_item[0] == "Feedback Cliente",
                                                                                                                "crimson",
                                                                                                                rx.cond(
                                                                                                                    category_item[0] == "Feedback Manager",
                                                                                                                    "grass",
                                                                                                                    "orange"
                                                                                                                )
                                                                                                            )
                                                                                                        )
                                                                                                    )
                                                                                                ), 11
                                                                                            ),
                                                                                            rx.color("gray", 11)
                                                                                        ),
                                                                                        font_weight=rx.cond(
                                                                                            comp_item[0] == DashboardState.selected_competency_id,
                                                                                            "bold",
                                                                                            "normal"
                                                                                        ),
                                                                                        _hover={
                                                                                            "background": rx.color(
                                                                                                rx.cond(
                                                                                                    category_item[0] == "Competencias Comportamentales",
                                                                                                    "crimson",
                                                                                                    rx.cond(
                                                                                                        category_item[0] == "Competencias Tecnicas",
                                                                                                        "violet",
                                                                                                        rx.cond(
                                                                                                            category_item[0] == "Aprendizaje",
                                                                                                            "indigo",
                                                                                                            rx.cond(
                                                                                                                category_item[0] == "Feedback Cliente",
                                                                                                                "crimson",
                                                                                                                rx.cond(
                                                                                                                    category_item[0] == "Feedback Manager",
                                                                                                                    "grass",
                                                                                                                    "orange"
                                                                                                                )
                                                                                                            )
                                                                                                        )
                                                                                                    )
                                                                                                ), 2
                                                                                            ),
                                                                                            "color": rx.color(
                                                                                                rx.cond(
                                                                                                    category_item[0] == "Competencias Comportamentales",
                                                                                                    "crimson",
                                                                                                    rx.cond(
                                                                                                        category_item[0] == "Competencias Tecnicas",
                                                                                                        "violet",
                                                                                                        rx.cond(
                                                                                                            category_item[0] == "Aprendizaje",
                                                                                                            "indigo",
                                                                                                            rx.cond(
                                                                                                                category_item[0] == "Feedback Cliente",
                                                                                                                "crimson",
                                                                                                                rx.cond(
                                                                                                                    category_item[0] == "Feedback Manager",
                                                                                                                    "grass",
                                                                                                                    "orange"
                                                                                                                )
                                                                                                            )
                                                                                                        )
                                                                                                    )
                                                                                                ), 11
                                                                                            )
                                                                                        },
                                                                                    ),
                                                                                    width="100%",
                                                                                ),
                                                                            ),
                                                                            width="100%",
                                                                            spacing="0",
                                                                            align_items="stretch",
                                                                        ),
                                                                        width="100%",
                                                                        spacing="2",
                                                                        margin_bottom="4",
                                                                    ),
                                                                ),
                                                                width="100%",
                                                                spacing="0",
                                                                align_items="stretch",
                                                            ),
                                                            width="100%",
                                                            spacing="1",
                                                            align_items="stretch",
                                                            margin_bottom="4",
                                                        ),
                                                        width="100%",
                                                    ),
                                                ),
                                            ),
                                            width="100%",
                                            height="calc(100vh - 4rem)",
                                            overflow_y="auto",
                                            overflow_x="hidden",
                                        ),
                                        width="18%",
                                        min_width="300px",
                                        height="calc(100vh - 4rem)",
                                        background_color=rx.color("mauve", 1),
                                        padding="1%",
                                        overflow_x="hidden",
                                    ),


                                    # Right content area - Competency Detail Display or Category Action Plans
                                    rx.box(
                                        rx.cond(
                                            DashboardState.selected_category != "",
                                            # Category Action Plans View
                                            rx.vstack(
                                                rx.hstack(
                                                    rx.heading(
                                                        f"Planes de Acción - {DashboardState.selected_category}",
                                                        size="4",
                                                    ),
                                                    rx.spacer(),
                                                    rx.badge(
                                                        DashboardState.selected_category,
                                                        color_scheme=rx.cond(
                                                            DashboardState.selected_category == "Competencias Comportamentales",
                                                            "crimson",
                                                            rx.cond(
                                                                DashboardState.selected_category == "Competencias Tecnicas",
                                                                "violet",
                                                                rx.cond(
                                                                    DashboardState.selected_category == "Aprendizaje",
                                                                    "indigo",
                                                                    rx.cond(
                                                                        DashboardState.selected_category == "Feedback Cliente",
                                                                        "crimson",
                                                                        rx.cond(
                                                                            DashboardState.selected_category == "Feedback Manager",
                                                                            "grass",
                                                                            "orange"
                                                                        )
                                                                    )
                                                                )
                                                            )
                                                        ),
                                                    ),
                                                    width="100%",
                                                    margin_bottom="4",
                                                ),
                                                # Visual Summary Section (Spider Chart)
                                                spider_chart_component(),
                                                # Category Action Plans organized by factor
                                                rx.cond(
                                                    DashboardState.get_action_plans_for_current_category,
                                                    rx.vstack(
                                                        rx.foreach(
                                                            DashboardState.get_action_plans_for_current_category.items(),
                                                            lambda factor_item: rx.vstack(
                                                                # Factor Header
                                                                rx.heading(
                                                                    factor_item[0],
                                                                    size="3",
                                                                    margin_top="4",
                                                                    margin_bottom="3",
                                                                    color=rx.color("gray", 11),
                                                                ),
                                                                rx.divider(),
                                                                # Action Plans for this factor
                                                                rx.foreach(
                                                                    factor_item[1],
                                                                    lambda action_plan: rx.card(
                                                                        rx.vstack(
                                                                            rx.hstack(
                                                                                rx.badge(
                                                                                    "Plan de Acción",
                                                                                    color_scheme="blue",
                                                                                    variant="solid",
                                                                                    size="1",
                                                                                ),
                                                                                rx.text(
                                                                                    "Creado por: ",
                                                                                    action_plan["evaluator_name"],
                                                                                    font_size="sm",
                                                                                    color="gray.600",
                                                                                ),
                                                                                width="100%",
                                                                                justify="between",
                                                                                align_items="center",
                                                                            ),
                                                                            rx.divider(),
                                                                            rx.text(
                                                                                action_plan["action_plan_text"],
                                                                                font_size="sm",
                                                                                line_height="1.6",
                                                                                white_space="pre-wrap",
                                                                            ),
                                                                            rx.text(
                                                                                "Actualizado: ",
                                                                                rx.cond(
                                                                                    action_plan["updated_at"],
                                                                                    action_plan["updated_at"],
                                                                                    action_plan["created_at"]
                                                                                ),
                                                                                font_size="xs",
                                                                                color="gray.500",
                                                                                margin_top="2",
                                                                            ),
                                                                            align_items="start",
                                                                            width="100%",
                                                                            spacing="3",
                                                                        ),
                                                                        width="100%",
                                                                        padding="4",
                                                                        border_radius="md",
                                                                        border_left="4px solid",
                                                                        border_left_color="blue.500",
                                                                        background="blue.50",
                                                                        margin_bottom="3",
                                                                    ),
                                                                ),
                                                                width="100%",
                                                                spacing="3",
                                                                margin_bottom="6",
                                                            ),
                                                        ),
                                                        width="100%",
                                                        spacing="4",
                                                        align_items="start",
                                                    ),
                                                    rx.center(
                                                        rx.vstack(
                                                            rx.icon(
                                                                tag="file-text",
                                                                font_size="3xl",
                                                                color="gray.400",
                                                                margin_bottom="4",
                                                            ),
                                                            rx.text(
                                                                f"No hay planes de acción disponibles para {DashboardState.selected_category}",
                                                                color="gray.500",
                                                                font_size="lg",
                                                                text_align="center",
                                                            ),
                                                            spacing="2",
                                                            align_items="center",
                                                        ),
                                                        height="50vh",
                                                    ),
                                                ),
                                                width="100%",
                                                spacing="4",
                                                align_items="start",
                                                padding="4%",
                                                height="calc(100vh - 4rem)",
                                                overflow_y="auto",
                                            ),
                                            rx.cond(
                                                DashboardState.get_selected_competency != None,
                                                rx.vstack(
                                                rx.hstack(
                                                    rx.heading(
                                                        DashboardState.get_selected_competency[1]["name"].to(str),
                                                        size="4",
                                                    ),
                                                    rx.spacer(),
                                                    rx.badge(
                                                        DashboardState.get_selected_competency[1]["category"].replace("_", " ").title(),
                                                        color_scheme=rx.cond(
                                                            DashboardState.get_selected_competency[1]["category"].replace("_", " ").title() == "Competencias Comportamentales",
                                                            "crimson",
                                                            rx.cond(
                                                                DashboardState.get_selected_competency[1]["category"].replace("_", " ").title() == "Competencias Tecnicas",
                                                                "violet",
                                                                rx.cond(
                                                                    DashboardState.get_selected_competency[1]["category"].replace("_", " ").title() == "Aprendizaje",
                                                                    "indigo",
                                                                    rx.cond(
                                                                        DashboardState.get_selected_competency[1]["category"].replace("_", " ").title() == "Feedback Cliente",
                                                                        "crimson",
                                                                        rx.cond(
                                                                            DashboardState.get_selected_competency[1]["category"].replace("_", " ").title() == "Feedback Manager",
                                                                            "grass",
                                                                            "orange"
                                                                        )
                                                                    )
                                                                )
                                                            )
                                                        ),
                                                    ),
                                                    width="100%",
                                                    margin_bottom="2",
                                                ),
                                                rx.hstack(
                                                        rx.text(DashboardState.get_selected_competency[1]["description"].to(str)),
                                                        width="100%",
                                                         height="100%",
                                                    ),

                                                rx.box(
                                                    rx.divider(
                                                        color=rx.color("gray", 4),
                                                        margin_y="4",
                                                        border_width="1px",
                                                    ),
                                                    width="100%",
                                                    padding_x="2",
                                                ),

                                                # Questions Section
                                                rx.cond(
                                                    DashboardState.is_loading_questions,
                                                    rx.center(rx.spinner(size="3")),
                                                    rx.cond(
                                                        DashboardState.competency_questions_by_behavior != None,
                                                        rx.vstack(
                                                            rx.heading("Preguntas por Tipo de Comportamiento", size="3", margin_top="4", margin_bottom="4"),
                                                            rx.foreach(
                                                                DashboardState.competency_questions_by_behavior.to(Dict[str, List[Dict[str, Any]]]).items(),
                                                                lambda behavior_item: rx.hstack(
                                                                    # Behavior Type Header
                                                                    rx.vstack(
                                                                        rx.divider(),
                                                                        rx.badge(
                                                                            rx.hstack(
                                                                                rx.cond(
                                                                                    behavior_item[0] == "expert",
                                                                                    rx.icon(tag="thumbs-up", size=20),
                                                                                    rx.cond(
                                                                                        behavior_item[0] == "talented",
                                                                                        rx.icon(tag="star", size=20),
                                                                                        rx.icon(tag="triangle-alert", size=20)
                                                                                    )
                                                                                ),
                                                                                rx.cond(
                                                                                    behavior_item[0] == "expert",
                                                                                    "Comportamiento Experto",
                                                                                    rx.cond(
                                                                                        behavior_item[0] == "talented",
                                                                                        "Comportamiento Talentoso",
                                                                                        "Comportamiento de Baja Habilidad"
                                                                                    )
                                                                                ),
                                                                                spacing="2",
                                                                                align_items="center",
                                                                            ),
                                                                            color_scheme=rx.cond(
                                                                                behavior_item[0] == "expert",
                                                                                "mint",
                                                                                rx.cond(
                                                                                    behavior_item[0] == "talented",
                                                                                    "grass",
                                                                                    "amber"
                                                                                )
                                                                            ),
                                                                            variant="surface",
                                                                            size="2",
                                                                        ),
                                                                        min_width="275px",
                                                                    ),
                                                                    # Questions for this behavior type
                                                                    rx.vstack(
                                                                        rx.foreach(
                                                                            behavior_item[1].to(List[Dict[str, Any]]),
                                                                            lambda question: rx.card(
                                                                                rx.vstack(
                                                                                    rx.text(
                                                                                        question["text"],
                                                                                        font_weight="medium",
                                                                                    ),
                                                                                    # Create badges with dynamic values
                                                                                    rx.hstack(
                                                                                        # Sí badge
                                                                                        rx.badge(
                                                                                            rx.hstack(
                                                                                                rx.text("Sí: "),
                                                                                                rx.text(question["response_counts"].to(Dict[str, int])["si"]),
                                                                                            ),
                                                                                            color_scheme="green",
                                                                                            variant="soft",
                                                                                            size="1",
                                                                                        ),
                                                                                        # No badge
                                                                                        rx.badge(
                                                                                            rx.hstack(
                                                                                                rx.text("No: "),
                                                                                                rx.text(question["response_counts"].to(Dict[str, int])["no"]),
                                                                                            ),
                                                                                            color_scheme="red",
                                                                                            variant="soft",
                                                                                            size="1",
                                                                                        ),
                                                                                        # A veces badge
                                                                                        rx.badge(
                                                                                            rx.hstack(
                                                                                                rx.text("A veces: "),
                                                                                                rx.text(question["response_counts"].to(Dict[str, int])["a_veces"]),
                                                                                            ),
                                                                                            color_scheme="yellow",
                                                                                            variant="soft",
                                                                                            size="1",
                                                                                        ),
                                                                                        spacing="2",
                                                                                    ),
                                                                                    align_items="start",
                                                                                    width="100%",
                                                                                    spacing="2",
                                                                                ),
                                                                                width="100%",
                                                                                border_radius="md",
                                                                                margin_bottom="2",
                                                                            ),
                                                                        ),
                                                                        width="100%",
                                                                        spacing="2",
                                                                    ),
                                                                    width="100%",
                                                                    margin_bottom="4",
                                                                    align_items="start",
                                                                ),
                                                            ),
                                                            justify="start",
                                                            width="100%",
                                                            align_items="start",
                                                        ),
                                                        rx.vstack(
                                                            rx.text(
                                                                "No hay preguntas disponibles para esta competencia",
                                                                color="gray.500",
                                                                font_style="italic",
                                                            ),
                                                            width="100%",
                                                            padding="4",
                                                            align_items="center",
                                                        ),
                                                    ),
                                                ),

                                                rx.box(
                                                    rx.divider(
                                                        color=rx.color("gray", 4),
                                                        margin_y="4",
                                                        border_width="1px",
                                                    ),
                                                    width="100%",
                                                    padding_x="2",
                                                ),

                                                # Comments Section
                                                rx.cond(
                                                    DashboardState.get_selected_competency[1]["comments"].to(List[CommentType]),
                                                    rx.vstack(
                                                        rx.heading("Comentarios recibidos", size="3", margin_top="4", margin_bottom="4"),
                                                        rx.grid(
                                                            rx.foreach(
                                                                DashboardState.get_selected_competency[1]["comments"].to(List[CommentType]),
                                                                lambda comment: rx.card(
                                                                    rx.vstack(
                                                                        rx.box(
                                                                            rx.text(
                                                                                #f"{comment['evaluator'].to(str)} ({comment['role'].to(str)}) - {comment['type'].to(str)}",
                                                                                f"Rol del Evaluador: {comment['role'].to(str)}",
                                                                                font_weight="medium",
                                                                                font_size="3",
                                                                                color="gray.600",
                                                                            ),
                                                                            width="100%",
                                                                            padding_bottom="2",
                                                                            border_color="gray.100",
                                                                        ),
                                                                        rx.divider(),
                                                                        rx.text(
                                                                            comment["text"].to(str),
                                                                            font_size="sm",
                                                                            padding_top="2",
                                                                        ),
                                                                        align_items="start",
                                                                        width="100%",
                                                                        spacing="2",
                                                                    ),
                                                                    height="auto",
                                                                    padding="3",
                                                                    border_radius="md",
                                                                    box_shadow="none",
                                                                    style={"transition": "all 0.2s ease"},
                                                                    _hover={"transform": "translateY(-3px)", "boxShadow": "0 4px 8px rgba(0, 0, 0, 0.1)"},
                                                                ),
                                                            ),
                                                            columns="1",
                                                            spacing="4",
                                                            width="100%",
                                                        ),
                                                        width="100%",
                                                        align_items="start",
                                                    ),
                                                    rx.text(""),  # Empty text when no comments
                                                ),

                                                # Action Plans Section - Removed from competency level
                                                # Action plans are now only displayed at category level
                                                width="100%",
                                                spacing="4",
                                                align_items="start",
                                                padding="4%",
                                                height="calc(100vh - 4rem)",
                                                overflow_y="auto",
                                                ),
                                                # Message when no competency is selected
                                                rx.center(
                                                    rx.vstack(
                                                        rx.icon(
                                                            tag="info",
                                                            font_size="3xl",
                                                            color="gray.400",
                                                            margin_bottom="4",
                                                        ),
                                                        rx.text(
                                                            "Selecciona una competencia del menú para ver sus detalles",
                                                            color="gray.500",
                                                            font_size="lg",
                                                        ),
                                                        spacing="2",
                                                        align_items="center",
                                                    ),
                                                    height="calc(100vh - 4rem)",
                                                ),
                                            ),
                                        ),
                                        width="100%",
                                        height="calc(100vh - 4rem)",
                                        overflow_x="hidden",
                                    ),
                                    width="100%",
                                    spacing="0",
                                    height="calc(100vh - 4rem)",
                                    overflow_x="hidden",
                                ),
                                width="100%",
                            ),
                            width="100%",
                            spacing="1",
                        ),
                        width="100%",
                        padding="0%",
                        height="100%",
                        #height="calc(100vh - 4rem)",  # Fixed height to match container
                        overflow_y="auto",  # Allow scrolling within content area
                        overflow_x="hidden"  # Hide horizontal scrollbar
                    ),
                    width="100%",
                    align_items="start",
                    spacing="4",
                ),
            ),
            width="100%",
            #height="calc(100vh - 4rem)",  # Fixed height to subtract navbar
            overflow="hidden",  # Prevent scrolling at this level
            spacing="0",
            align_items="flex-start",
        ),
        width="100%",
        #height="calc(100vh - 4rem)",  # Fixed height to subtract navbar
        margin="0",
        padding="0",
        spacing="0",
        overflow_y="hidden",  # Prevent scrolling at page level
        position="relative",  # Ensure proper positioning
        style={"height": "100vh", "overflow": "hidden"}  # Additional style to prevent page scrolling
        ),
    )